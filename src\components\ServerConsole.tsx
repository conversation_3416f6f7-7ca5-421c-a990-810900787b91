'use client';

import { useState, useEffect, useRef } from 'react';
import { getServerLogs, sendServerCommand } from '../services/api';

interface ServerConsoleProps {
  serverId: string;
  isRunning: boolean;
}

export default function ServerConsole({ serverId, isRunning }: ServerConsoleProps) {
  const [logs, setLogs] = useState<string>('');
  const [command, setCommand] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const consoleRef = useRef<HTMLDivElement>(null);

  // Fetch logs on component mount and when server status changes
  useEffect(() => {
    const fetchLogs = async () => {
      try {
        const response = await getServerLogs(serverId);
        setLogs(response.logs || 'No logs available');
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch logs');
        console.error('Error fetching logs:', err);
      }
    };

    fetchLogs();

    // Set up polling for logs if server is running
    let interval: NodeJS.Timeout | null = null;
    if (isRunning) {
      interval = setInterval(fetchLogs, 5000); // Poll every 5 seconds
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [serverId, isRunning]);

  // Auto-scroll to bottom when logs update
  useEffect(() => {
    if (consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
    }
  }, [logs]);

  const handleSendCommand = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!command.trim() || !isRunning) return;

    setIsLoading(true);

    try {
      const response = await sendServerCommand(serverId, command);

      // Add the command and response to the logs
      setLogs(prev => `${prev}\n> ${command}\n${response.response}`);

      // Clear the command input
      setCommand('');
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send command');
      console.error('Error sending command:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="card overflow-hidden">
      <div className="px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
        <h3 className="text-xl font-bold flex items-center gradient-text">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          Server Console
        </h3>
        <p className="text-gray-300 mt-2 font-light">
          {isRunning ? 'Interactive console for server management' : 'Server is not running. Start the server to send commands.'}
        </p>
      </div>

      {error && (
        <div className="bg-red-500/10 border-l-4 border-red-500 p-6 m-6 rounded-lg backdrop-blur-sm">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-red-400 font-semibold">Console Error</h4>
              <p className="text-sm text-red-300 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Console Output */}
      <div
        ref={consoleRef}
        className="bg-black/50 text-green-400 font-mono text-sm p-6 h-96 overflow-y-auto border border-white/5 m-6 rounded-lg backdrop-blur-sm"
        style={{ fontFamily: 'JetBrains Mono, Consolas, Monaco, "Courier New", monospace' }}
      >
        <div className="flex items-center mb-4 pb-2 border-b border-green-500/20">
          <div className="flex space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <span className="ml-4 text-green-300 text-xs font-semibold">Server Console</span>
        </div>
        <pre className="whitespace-pre-wrap break-words text-green-300 leading-relaxed">
          {logs || (
            <span className="text-gray-500 italic">
              {isRunning ? 'Loading logs...' : 'Server is not running. No logs available.'}
            </span>
          )}
        </pre>
      </div>

      {/* Command Input */}
      <form onSubmit={handleSendCommand} className="flex items-center bg-black/30 border-t border-white/10 backdrop-blur-sm">
        <div className="text-green-400 px-6 py-4 select-none font-mono font-bold text-lg">&gt;</div>
        <input
          type="text"
          value={command}
          onChange={(e) => setCommand(e.target.value)}
          placeholder={isRunning ? "Type a command..." : "Server is not running"}
          disabled={!isRunning || isLoading}
          className="flex-1 bg-transparent text-white px-2 py-4 focus:outline-none font-mono placeholder-gray-500 focus:placeholder-gray-400"
        />
        <button
          type="submit"
          disabled={!isRunning || isLoading || !command.trim()}
          className="btn-primary mx-4 my-2 px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          {isLoading ? (
            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <span className="flex items-center">
              <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              Send
            </span>
          )}
        </button>
      </form>
    </div>
  );
}
