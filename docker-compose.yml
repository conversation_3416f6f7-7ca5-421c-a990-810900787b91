services:
  # Frontend - Next.js Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: minecraft-admin-frontend
    ports:
      - "3001:3001"
    environment:
      # API Configuration - Points to backend service
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api
      # Firebase Configuration (Frontend) - Auth-only setup
      - NEXT_PUBLIC_FIREBASE_API_KEY=${NEXT_PUBLIC_FIREBASE_API_KEY}
      - NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=${NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}
      - NEXT_PUBLIC_FIREBASE_PROJECT_ID=${NEXT_PUBLIC_FIREBASE_PROJECT_ID}
      - NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}
      - NEXT_PUBLIC_FIREBASE_APP_ID=${NEXT_PUBLIC_FIREBASE_APP_ID}
      # Production settings
      - NODE_ENV=production
      - PORT=3001
      - HOSTNAME=0.0.0.0
    depends_on:
      - backend
    networks:
      - minecraft-admin-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend - Flask Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: minecraft-admin-backend
    ports:
      - "5000:5000"
    environment:
      # Flask Configuration
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5000
      - FLASK_LOG_LEVEL=INFO
      # Firebase Admin Configuration (Backend)
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY_ID=${FIREBASE_PRIVATE_KEY_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - FIREBASE_CLIENT_ID=${FIREBASE_CLIENT_ID}
      - FIREBASE_AUTH_URI=${FIREBASE_AUTH_URI}
      - FIREBASE_TOKEN_URI=${FIREBASE_TOKEN_URI}
      - FIREBASE_AUTH_PROVIDER_X509_CERT_URL=${FIREBASE_AUTH_PROVIDER_X509_CERT_URL}
      - FIREBASE_CLIENT_X509_CERT_URL=${FIREBASE_CLIENT_X509_CERT_URL}
    volumes:
      # Mount server data for persistence
      - minecraft-servers:/app/servers
      # Mount logs for debugging (optional)
      - ./logs:/app/logs
    networks:
      - minecraft-admin-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# Networks
networks:
  minecraft-admin-network:
    driver: bridge
    name: minecraft-admin-network

# Volumes
volumes:
  minecraft-servers:
    driver: local
    name: minecraft-servers
