services:
  # Frontend - Development Configuration
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend.dev
      target: development
    container_name: minecraft-admin-frontend-dev
    environment:
      # Development settings
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - /app/node_modules
      - /app/.next
    command: npm run dev
    ports:
      - "3001:3001"
    stdin_open: true
    tty: true

  # Backend - Development Configuration
  backend:
    environment:
      # Development Flask settings
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - FLASK_LOG_LEVEL=DEBUG
    volumes:
      # Mount source code for hot reloading
      - ./backend:/app
      # Mount server data for persistence
      - minecraft-servers:/app/servers
      # Mount logs for debugging
      - ./logs:/app/logs
    command: python app.py
