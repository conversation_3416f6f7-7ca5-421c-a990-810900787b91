{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { user, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !user) {\n      router.push('/login');\n    }\n  }, [user, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center fade-in-up\">\n          <div className=\"relative mb-6\">\n            <div className=\"animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto\"></div>\n            <div className=\"absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto\"></div>\n          </div>\n          <h3 className=\"text-xl font-semibold text-white mb-2\">Loading</h3>\n          <p className=\"text-gray-300 font-light\">Please wait...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,MAAM;gBACvB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAA2B;;;;;;;;;;;;;;;;;IAIhD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,qBAAO;kBAAG;;AACZ;GA9BwB;;QACM,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/AdminHeader.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\n\nexport default function AdminHeader() {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = () => {\n    logout();\n    router.push('/login');\n  };\n\n  return (\n    <header className=\"relative overflow-hidden\">\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90 backdrop-blur-sm\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20\"></div>\n\n      <div className=\"relative z-10 container mx-auto flex justify-between items-center p-6\">\n        <div className=\"flex items-center fade-in-up\">\n          <div className=\"w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-br from-blue-400/20 to-purple-400/20 backdrop-blur-sm border border-white/10\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-7 w-7 text-blue-300\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm3 1h6v6H6V5z\" clipRule=\"evenodd\" />\n              <path d=\"M6 5H5v1h1V5z\" />\n              <path d=\"M5 8h1v1H5V8z\" />\n              <path d=\"M8 5h1v1H8V5z\" />\n              <path d=\"M8 8h1v1H8V8z\" />\n              <path d=\"M11 5h1v1h-1V5z\" />\n              <path d=\"M11 8h1v1h-1V8z\" />\n              <path d=\"M5 11h1v1H5v-1z\" />\n              <path d=\"M8 11h1v1H8v-1z\" />\n              <path d=\"M11 11h1v1h-1v-1z\" />\n            </svg>\n          </div>\n          <div>\n            <h1 className=\"text-2xl font-bold text-white\">Minecraft Server Admin Panel</h1>\n            <p className=\"text-blue-200 text-sm font-medium\">Welcome, <span className=\"gradient-text-secondary\">{user?.email || user?.displayName || 'User'}</span></p>\n          </div>\n        </div>\n\n        <nav className=\"flex items-center space-x-4 fade-in-up delay-200\">\n          <Link\n            href=\"/dashboard\"\n            className=\"nav-link text-white/90 hover:text-white font-medium px-4 py-2 rounded-xl hover:bg-white/10 transition-all duration-300\"\n          >\n            <svg className=\"h-5 w-5 mr-2 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n            </svg>\n            Dashboard\n          </Link>\n          <button\n            onClick={handleLogout}\n            className=\"btn-secondary px-6 py-2 font-semibold inline-flex items-center hover:scale-105 transition-all duration-300\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z\" clipRule=\"evenodd\" />\n            </svg>\n            Logout\n          </button>\n        </nav>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAwB,SAAQ;oCAAY,MAAK;;sDACjG,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAiF,UAAS;;;;;;sDACrH,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;0CAGZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAE,WAAU;;4CAAoC;0DAAS,6LAAC;gDAAK,WAAU;0DAA2B,MAAM,SAAS,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;kCAI7I,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAsB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;0CAGR,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAe,SAAQ;wCAAY,MAAK;kDACxF,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA6K,UAAS;;;;;;;;;;;oCAC7M;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA3DwB;;QACG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/services/api.ts"], "sourcesContent": ["'use client';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nexport interface ServerProperties {\n  [key: string]: string;\n}\n\nexport interface PlayerInfo {\n  online: number;\n  max: number;\n  list: string[];\n}\n\nexport interface Server {\n  id: string;\n  name: string;\n  port: number;\n  version: string;\n  memory: string;\n  status: string;\n  backup?: boolean;\n  container_id?: string;\n  properties?: ServerProperties;\n  players?: PlayerInfo;\n}\n\nexport interface ServerFormData {\n  name: string;\n  port: number;\n  version: string;\n  memory: string;\n}\n\nexport async function getServers(): Promise<Server[]> {\n  try {\n    console.log('Fetching servers from:', API_URL);\n    const response = await fetch(`${API_URL}/servers`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      cache: 'no-store',\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch servers: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching servers:', error);\n    throw error;\n  }\n}\n\nexport async function createServer(data: ServerFormData): Promise<Server> {\n  try {\n    const response = await fetch(`${API_URL}/servers`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to create server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating server:', error);\n    throw error;\n  }\n}\n\nexport async function startServer(serverName: string): Promise<{ status: string; message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}/start`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to start server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error starting server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function stopServer(serverName: string): Promise<{ status: string; message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}/stop`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to stop server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error stopping server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function deleteServer(serverName: string): Promise<{ message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to delete server: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error deleting server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function toggleBackup(serverName: string): Promise<{ message: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverName}/backup`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to toggle backup: ${response.status} ${response.statusText}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(`Error toggling backup for server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function downloadBackup(serverName: string): Promise<void> {\n  try {\n    // Create a direct link to the backup endpoint\n    const backupUrl = `${API_URL}/servers/${serverName}/backup`;\n\n    // Open the URL in a new tab/window to trigger the download\n    window.open(backupUrl, '_blank');\n  } catch (error) {\n    console.error(`Error downloading backup for server ${serverName}:`, error);\n    throw error;\n  }\n}\n\nexport async function getServerDetails(serverId: string): Promise<Server> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverId}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      cache: 'no-store',\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to fetch server details: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error fetching server details for ${serverId}:`, error);\n    throw error;\n  }\n}\n\nexport async function getServerLogs(serverId: string): Promise<{ logs: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverId}/logs`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      cache: 'no-store',\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to fetch server logs: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error fetching logs for server ${serverId}:`, error);\n    throw error;\n  }\n}\n\nexport async function sendServerCommand(serverId: string, command: string): Promise<{ response: string }> {\n  try {\n    const response = await fetch(`${API_URL}/servers/${serverId}/command`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n      body: JSON.stringify({ command }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.error || `Failed to send command: ${response.status} ${response.statusText}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(`Error sending command to server ${serverId}:`, error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEgB;AAFhB;AAEA,MAAM,UAAU,iEAAmC;AAgC5C,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC,0BAA0B;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,CAAC,EAAE;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACtF;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,eAAe,aAAa,IAAoB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,CAAC,EAAE;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACzG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,YAAY,UAAkB;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,WAAW,MAAM,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;QACtD,MAAM;IACR;AACF;AAEO,eAAe,WAAW,UAAkB;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,WAAW,KAAK,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACvG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;QACtD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,UAAkB;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,YAAY,EAAE;YAC/D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACzG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;QACtD,MAAM;IACR;AACF;AAEO,eAAe,aAAa,UAAkB;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,WAAW,OAAO,CAAC,EAAE;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACzG;QACA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,WAAW,CAAC,CAAC,EAAE;QACjE,MAAM;IACR;AACF;AAEO,eAAe,eAAe,UAAkB;IACrD,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY,GAAG,QAAQ,SAAS,EAAE,WAAW,OAAO,CAAC;QAE3D,2DAA2D;QAC3D,OAAO,IAAI,CAAC,WAAW;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,WAAW,CAAC,CAAC,EAAE;QACpE,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,QAAgB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,gCAAgC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAChH;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;QAChE,MAAM;IACR;AACF;AAEO,eAAe,cAAc,QAAgB;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,SAAS,KAAK,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,6BAA6B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC7G;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,EAAE;QAC7D,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,QAAgB,EAAE,OAAe;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,EAAE,SAAS,QAAQ,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,wBAAwB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxG;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC9D,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/ServerForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { createServer, ServerFormData } from '../services/api';\n\n// Define common Minecraft versions\nconst MINECRAFT_VERSIONS = [\n  '1.21.5',\n  '1.21.4',\n  '1.20.6',\n  '1.20.4',\n  '1.19.4',\n  '1.19.2',\n  '1.18.2',\n  '1.17.1',\n  '1.16.5',\n  '1.15.2',\n  '1.14.4',\n  '1.12.2',\n  'latest'\n];\n\n// Define memory options\nconst MEMORY_OPTIONS = [\n  '1G',\n  '2G',\n  '4G',\n  '6G',\n  '8G',\n  '12G',\n  '16G'\n];\n\ninterface ServerFormProps {\n  onServerCreated: () => void;\n}\n\nexport default function ServerForm({ onServerCreated }: ServerFormProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const { register, handleSubmit, reset, formState: { errors } } = useForm<ServerFormData>({\n    defaultValues: {\n      version: MINECRAFT_VERSIONS[0], // Default to the first version in the list\n      memory: '4G' // Default to 4G of memory\n    }\n  });\n\n  const onSubmit = async (data: ServerFormData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      await createServer(data);\n      reset();\n      onServerCreated();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"card overflow-hidden\">\n      <div className=\"px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n        <h2 className=\"text-2xl font-bold flex items-center gradient-text\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n          Create New Server\n        </h2>\n        <p className=\"text-gray-300 mt-2 font-light\">Configure and deploy a new Minecraft server</p>\n      </div>\n\n      <div className=\"p-8\">\n        {error && (\n          <div className=\"bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg mb-6 flex items-start backdrop-blur-sm\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 flex-shrink-0\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            <span>{error}</span>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"name\">\n              Server Name\n            </label>\n            <input\n              className=\"input-field w-full\"\n              id=\"name\"\n              type=\"text\"\n              placeholder=\"My Minecraft Server\"\n              {...register('name', { required: 'Server name is required' })}\n            />\n            {errors.name && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.name.message}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"port\">\n              Port\n            </label>\n            <input\n              className=\"input-field w-full\"\n              id=\"port\"\n              type=\"number\"\n              placeholder=\"25565\"\n              {...register('port', {\n                required: 'Port is required',\n                min: { value: 1024, message: 'Port must be at least 1024' },\n                max: { value: 65535, message: 'Port must be at most 65535' }\n              })}\n            />\n            {errors.port && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.port.message}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"version\">\n              Minecraft Version\n            </label>\n            <select\n              className=\"input-field w-full\"\n              id=\"version\"\n              {...register('version', { required: 'Version is required' })}\n            >\n              <option value=\"\" disabled>Select a Minecraft version</option>\n              <option value={MINECRAFT_VERSIONS[0]}>{MINECRAFT_VERSIONS[0]} (Latest)</option>\n              {MINECRAFT_VERSIONS.filter(version => version !== MINECRAFT_VERSIONS[0]).map((version) => (\n                <option key={version} value={version}>\n                  {version}\n                </option>\n              ))}\n            </select>\n            {errors.version && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.version.message}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"memory\">\n              Memory Allocation\n            </label>\n            <select\n              className=\"input-field w-full\"\n              id=\"memory\"\n              {...register('memory', { required: 'Memory is required' })}\n            >\n              <option value=\"\" disabled>Select memory allocation</option>\n              {MEMORY_OPTIONS.map((memory) => (\n                <option key={memory} value={memory}>\n                  {memory}\n                </option>\n              ))}\n            </select>\n            {errors.memory && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.memory.message}\n              </p>\n            )}\n          </div>\n\n          <div className=\"pt-4\">\n            <button\n              className=\"btn-primary w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n              type=\"submit\"\n              disabled={isLoading}\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Creating Server...\n                </div>\n              ) : (\n                <span className=\"flex items-center justify-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Create Server\n                </span>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,mCAAmC;AACnC,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,wBAAwB;AACxB,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAMc,SAAS,WAAW,EAAE,eAAe,EAAmB;;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QACvF,eAAe;YACb,SAAS,kBAAkB,CAAC,EAAE;YAC9B,QAAQ,KAAK,0BAA0B;QACzC;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE;YACnB;YACA;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAA6B,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACpH,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;kCAGR,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;;oBACZ,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAAoC,SAAQ;gCAAY,MAAK;0CAC7G,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;0CAE1J,6LAAC;0CAAM;;;;;;;;;;;;kCAIX,6LAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;0CAChD,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;wCAAiD,SAAQ;kDAAO;;;;;;kDAGjF,6LAAC;wCACC,WAAU;wCACV,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,SAAS,QAAQ;4CAAE,UAAU;wCAA0B,EAAE;;;;;;oCAE9D,OAAO,IAAI,kBACV,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAe,SAAQ;0DACxD,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoH,UAAS;;;;;;;;;;;4CAEzJ,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;0CAK1B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;wCAAiD,SAAQ;kDAAO;;;;;;kDAGjF,6LAAC;wCACC,WAAU;wCACV,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,SAAS,QAAQ;4CACnB,UAAU;4CACV,KAAK;gDAAE,OAAO;gDAAM,SAAS;4CAA6B;4CAC1D,KAAK;gDAAE,OAAO;gDAAO,SAAS;4CAA6B;wCAC7D,EAAE;;;;;;oCAEH,OAAO,IAAI,kBACV,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAe,SAAQ;0DACxD,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoH,UAAS;;;;;;;;;;;4CAEzJ,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;0CAK1B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;wCAAiD,SAAQ;kDAAU;;;;;;kDAGpF,6LAAC;wCACC,WAAU;wCACV,IAAG;wCACF,GAAG,SAAS,WAAW;4CAAE,UAAU;wCAAsB,EAAE;;0DAE5D,6LAAC;gDAAO,OAAM;gDAAG,QAAQ;0DAAC;;;;;;0DAC1B,6LAAC;gDAAO,OAAO,kBAAkB,CAAC,EAAE;;oDAAG,kBAAkB,CAAC,EAAE;oDAAC;;;;;;;4CAC5D,mBAAmB,MAAM,CAAC,CAAA,UAAW,YAAY,kBAAkB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,wBAC5E,6LAAC;oDAAqB,OAAO;8DAC1B;mDADU;;;;;;;;;;;oCAKhB,OAAO,OAAO,kBACb,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAe,SAAQ;0DACxD,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoH,UAAS;;;;;;;;;;;4CAEzJ,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;;0CAK7B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;wCAAiD,SAAQ;kDAAS;;;;;;kDAGnF,6LAAC;wCACC,WAAU;wCACV,IAAG;wCACF,GAAG,SAAS,UAAU;4CAAE,UAAU;wCAAqB,EAAE;;0DAE1D,6LAAC;gDAAO,OAAM;gDAAG,QAAQ;0DAAC;;;;;;4CACzB,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;oDAAoB,OAAO;8DACzB;mDADU;;;;;;;;;;;oCAKhB,OAAO,MAAM,kBACZ,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAe,SAAQ;0DACxD,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoH,UAAS;;;;;;;;;;;4CAEzJ,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,UAAU;8CAET,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,6LAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,6LAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;;;;;6DAIR,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,SAAQ;gDAAY,MAAK;0DACxF,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAwF,UAAS;;;;;;;;;;;4CACxH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GAhLwB;;QAI2C,iKAAA,CAAA,UAAO;;;KAJlD", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/ServerList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { getServers, startServer, stopServer, deleteServer, toggleBackup, downloadBackup, Server } from '../services/api';\nimport Link from 'next/link';\n\ninterface ServerListProps {\n  refreshTrigger: number;\n}\n\nexport default function ServerList({ refreshTrigger }: ServerListProps) {\n  const [servers, setServers] = useState<Server[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchServers = async () => {\n      setIsLoading(true);\n      setError(null);\n\n      try {\n        const data = await getServers();\n        setServers(data);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An unknown error occurred');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchServers();\n  }, [refreshTrigger]);\n\n  const handleStartServer = async (serverName: string) => {\n    try {\n      await startServer(serverName);\n\n      // Update server status in the list\n      setServers(servers.map(server =>\n        server.name === serverName ? { ...server, status: 'running' } : server\n      ));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleStopServer = async (serverName: string) => {\n    try {\n      await stopServer(serverName);\n\n      // Update server status in the list\n      setServers(servers.map(server =>\n        server.name === serverName ? { ...server, status: 'stopped' } : server\n      ));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleDeleteServer = async (serverName: string) => {\n    if (!confirm(`Are you sure you want to delete the server \"${serverName}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await deleteServer(serverName);\n\n      // Remove server from the list\n      setServers(servers.filter(server => server.name !== serverName));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleToggleBackup = async (serverName: string) => {\n    try {\n      await toggleBackup(serverName);\n\n      // Update backup status in the list\n      setServers(servers.map(server =>\n        server.name === serverName ? { ...server, backup: !server.backup } : server\n      ));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleDownloadBackup = async (serverName: string) => {\n    try {\n      await downloadBackup(serverName);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'running':\n        return 'text-green-400 bg-green-500/20 border-green-500/30';\n      case 'stopped':\n        return 'text-red-400 bg-red-500/20 border-red-500/30';\n      case 'starting':\n        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';\n      default:\n        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-16\">\n        <div className=\"relative\">\n          <div className=\"animate-spin h-12 w-12 border-4 border-blue-500/30 border-t-blue-500 rounded-full\"></div>\n          <div className=\"absolute inset-0 animate-ping h-12 w-12 border-4 border-blue-500/20 rounded-full\"></div>\n        </div>\n        <span className=\"ml-4 text-gray-300 font-medium\">Loading servers...</span>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-500/10 border border-red-500/30 text-red-400 px-6 py-4 rounded-lg backdrop-blur-sm\">\n        <div className=\"flex items-center\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n          <div>\n            <h3 className=\"font-semibold\">Error Loading Servers</h3>\n            <p className=\"text-sm text-red-300\">{error}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (servers.length === 0) {\n    return (\n      <div className=\"text-center py-16\">\n        <div className=\"w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-10 w-10 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01\" />\n          </svg>\n        </div>\n        <h3 className=\"text-xl font-semibold text-white mb-2\">No servers found</h3>\n        <p className=\"text-gray-400 font-light\">Create your first Minecraft server to get started.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {servers.map((server, index) => (\n        <div key={server.id} className={`card p-6 hover:scale-[1.02] transition-all duration-300 fade-in-up delay-${index * 100}`}>\n          {/* Server Header */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center\">\n              <h3 className=\"text-xl font-bold text-white mr-4\">{server.name}</h3>\n              <span className={`px-4 py-2 rounded-full text-sm font-semibold border backdrop-blur-sm ${getStatusColor(server.status)}`}>\n                {server.status.charAt(0).toUpperCase() + server.status.slice(1)}\n              </span>\n            </div>\n          </div>\n\n          {/* Server Info Grid */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-6\">\n            <div className=\"bg-blue-500/10 rounded-lg p-3 border border-blue-500/20\">\n              <span className=\"text-blue-300 font-medium block\">Port</span>\n              <span className=\"text-white font-semibold\">{server.port}</span>\n            </div>\n            <div className=\"bg-purple-500/10 rounded-lg p-3 border border-purple-500/20\">\n              <span className=\"text-purple-300 font-medium block\">Version</span>\n              <span className=\"text-white font-semibold\">{server.version}</span>\n            </div>\n            <div className=\"bg-indigo-500/10 rounded-lg p-3 border border-indigo-500/20\">\n              <span className=\"text-indigo-300 font-medium block\">Memory</span>\n              <span className=\"text-white font-semibold\">{server.memory}</span>\n            </div>\n            <div className=\"bg-emerald-500/10 rounded-lg p-3 border border-emerald-500/20\">\n              <span className=\"text-emerald-300 font-medium block\">Backup</span>\n              <span className=\"text-white font-semibold\">{server.backup ? 'Enabled' : 'Disabled'}</span>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-wrap items-center gap-2 justify-end border-t border-white/10 pt-4\">\n            {server.status === 'stopped' ? (\n              <button\n                onClick={() => handleStartServer(server.name)}\n                className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-green-500/20 hover:bg-green-500/30 border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 hover:shadow-lg hover:shadow-green-500/20\"\n              >\n                <svg className=\"h-4 w-4 mr-1.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n                </svg>\n                Start\n              </button>\n            ) : (\n              <button\n                onClick={() => handleStopServer(server.name)}\n                className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-red-500/20 hover:bg-red-500/30 border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 hover:shadow-lg hover:shadow-red-500/20\"\n              >\n                <svg className=\"h-4 w-4 mr-1.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z\" clipRule=\"evenodd\" />\n                </svg>\n                Stop\n              </button>\n            )}\n\n            <button\n              onClick={() => handleToggleBackup(server.name)}\n              className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 ${\n                server.backup\n                  ? 'bg-blue-500/20 hover:bg-blue-500/30 border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-500/20'\n                  : 'bg-gray-500/20 hover:bg-gray-500/30 border-gray-500/30 hover:border-gray-500/50 text-gray-400 hover:text-gray-300 hover:shadow-lg hover:shadow-gray-500/20'\n              }`}\n            >\n              <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n              </svg>\n              {server.backup ? 'Backup On' : 'Backup Off'}\n            </button>\n\n            {server.backup && (\n              <button\n                onClick={() => handleDownloadBackup(server.name)}\n                className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-purple-500/20 hover:bg-purple-500/30 border-purple-500/30 hover:border-purple-500/50 text-purple-400 hover:text-purple-300 hover:shadow-lg hover:shadow-purple-500/20\"\n              >\n                <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                Download\n              </button>\n            )}\n\n            <Link\n              href={`/server/${server.id}`}\n              className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-indigo-500/20 hover:bg-indigo-500/30 border-indigo-500/30 hover:border-indigo-500/50 text-indigo-400 hover:text-indigo-300 hover:shadow-lg hover:shadow-indigo-500/20\"\n            >\n              <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              Details\n            </Link>\n\n            <button\n              onClick={() => handleDeleteServer(server.name)}\n              className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-red-500/20 hover:bg-red-500/30 border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 hover:shadow-lg hover:shadow-red-500/20\"\n            >\n              <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16\" />\n              </svg>\n              Delete\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,WAAW,EAAE,cAAc,EAAmB;;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,aAAa;oBACb,SAAS;oBAET,IAAI;wBACF,MAAM,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD;wBAC5B,WAAW;oBACb,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;+BAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;YAElB,mCAAmC;YACnC,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,IAAI,KAAK,aAAa;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU,IAAI;QAEpE,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;YAEjB,mCAAmC;YACnC,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,IAAI,KAAK,aAAa;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU,IAAI;QAEpE,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,CAAC,4CAA4C,EAAE,WAAW,gCAAgC,CAAC,GAAG;YACzG;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE;YAEnB,8BAA8B;YAC9B,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;QACtD,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE;YAEnB,mCAAmC;YACnC,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,IAAI,KAAK,aAAa;oBAAE,GAAG,MAAM;oBAAE,QAAQ,CAAC,OAAO,MAAM;gBAAC,IAAI;QAEzE,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;QACvB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,6LAAC;oBAAK,WAAU;8BAAiC;;;;;;;;;;;;IAGvD;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,OAAM;wBAA6B,WAAU;wBAAe,SAAQ;wBAAY,MAAK;kCACxF,cAAA,6LAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAoH,UAAS;;;;;;;;;;;kCAE1J,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,OAAM;wBAA6B,WAAU;wBAA0B,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACjH,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAK,GAAE;;;;;;;;;;;;;;;;8BAG3E,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAA2B;;;;;;;;;;;;IAG9C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;gBAAoB,WAAW,CAAC,yEAAyE,EAAE,QAAQ,KAAK;;kCAEvH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqC,OAAO,IAAI;;;;;;8CAC9D,6LAAC;oCAAK,WAAW,CAAC,qEAAqE,EAAE,eAAe,OAAO,MAAM,GAAG;8CACrH,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;kCAMnE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;kDAClD,6LAAC;wCAAK,WAAU;kDAA4B,OAAO,IAAI;;;;;;;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;kDAA4B,OAAO,OAAO;;;;;;;;;;;;0CAE5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;kDAA4B,OAAO,MAAM;;;;;;;;;;;;0CAE3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,6LAAC;wCAAK,WAAU;kDAA4B,OAAO,MAAM,GAAG,YAAY;;;;;;;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,MAAM,KAAK,0BACjB,6LAAC;gCACC,SAAS,IAAM,kBAAkB,OAAO,IAAI;gCAC5C,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAiB,MAAK;wCAAe,SAAQ;kDAC1D,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0G,UAAS;;;;;;;;;;;oCAC1I;;;;;;qDAIR,6LAAC;gCACC,SAAS,IAAM,iBAAiB,OAAO,IAAI;gCAC3C,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAiB,MAAK;wCAAe,SAAQ;kDAC1D,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAmG,UAAS;;;;;;;;;;;oCACnI;;;;;;;0CAKV,6LAAC;gCACC,SAAS,IAAM,mBAAmB,OAAO,IAAI;gCAC7C,WAAW,CAAC,qHAAqH,EAC/H,OAAO,MAAM,GACT,+JACA,8JACJ;;kDAEF,6LAAC;wCAAI,WAAU;wCAAiB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACxE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCAEtE,OAAO,MAAM,GAAG,cAAc;;;;;;;4BAGhC,OAAO,MAAM,kBACZ,6LAAC;gCACC,SAAS,IAAM,qBAAqB,OAAO,IAAI;gCAC/C,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAiB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACxE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;0CAKV,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;gCAC5B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAiB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACxE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;0CAIR,6LAAC;gCACC,SAAS,IAAM,mBAAmB,OAAO,IAAI;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;wCAAiB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACxE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;eAjGF,OAAO,EAAE;;;;;;;;;;AAyG3B;GAxPwB;KAAA", "debugId": null}}, {"offset": {"line": 1794, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport ProtectedRoute from '../../components/ProtectedRoute';\nimport AdminHeader from '../../components/AdminHeader';\nimport ServerForm from '../../components/ServerForm';\nimport ServerList from '../../components/ServerList';\n\nexport default function DashboardPage() {\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  const handleServerCreated = () => {\n    // Increment the refresh trigger to cause the ServerList to refresh\n    setRefreshTrigger(prev => prev + 1);\n  };\n\n  return (\n    <ProtectedRoute>\n      <div className=\"min-h-screen text-white\">\n        <AdminHeader />\n\n        <main className=\"container mx-auto py-12 px-4 max-w-7xl\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-10\">\n            {/* Server Creation Form */}\n            <div className=\"lg:col-span-1 fade-in-up\">\n              <ServerForm onServerCreated={handleServerCreated} />\n            </div>\n\n            {/* Server List */}\n            <div className=\"lg:col-span-2 fade-in-up delay-200\">\n              <div className=\"card overflow-hidden\">\n                <div className=\"px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n                  <h2 className=\"text-2xl font-bold flex items-center gradient-text\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01\" />\n                    </svg>\n                    Your Minecraft Servers\n                  </h2>\n                  <p className=\"text-gray-300 mt-2 font-light\">Manage and monitor your server instances</p>\n                </div>\n                <div className=\"p-8\">\n                  <ServerList refreshTrigger={refreshTrigger} />\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n\n        <footer className=\"relative mt-20\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm\"></div>\n          <div className=\"relative z-10 container mx-auto text-center py-8 px-4\">\n            <div className=\"border-t border-white/10 pt-8\">\n              <p className=\"text-sm text-gray-300 font-medium\">\n                Minecraft Server Admin Panel &copy; {new Date().getFullYear()}\n              </p>\n              <p className=\"text-xs mt-2 text-gray-400 font-light\">\n                Manage your Minecraft servers with ease\n              </p>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,sBAAsB;QAC1B,mEAAmE;QACnE,kBAAkB,CAAA,OAAQ,OAAO;IACnC;IAEA,qBACE,6LAAC,uIAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oIAAA,CAAA,UAAW;;;;;8BAEZ,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,UAAU;oCAAC,iBAAiB;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA6B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEACpH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mIAAA,CAAA,UAAU;gDAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOtC,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAoC;4CACV,IAAI,OAAO,WAAW;;;;;;;kDAE7D,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAxDwB;KAAA", "debugId": null}}]}