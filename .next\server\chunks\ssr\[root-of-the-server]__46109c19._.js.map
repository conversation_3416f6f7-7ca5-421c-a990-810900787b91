{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useForm } from 'react-hook-form';\nimport { useAuth } from '../../contexts/AuthContext';\n\ninterface LoginFormData {\n  email: string;\n  password: string;\n}\n\nexport default function LoginPage() {\n  const [error, setError] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSignUp, setIsSignUp] = useState(false);\n  const router = useRouter();\n  const { user, signIn, signUp, signInWithGoogle } = useAuth();\n\n  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormData>();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (user) {\n      router.push('/dashboard');\n    }\n  }, [user, router]);\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = isSignUp\n        ? await signUp(data.email, data.password)\n        : await signIn(data.email, data.password);\n\n      if (result.error) {\n        setError(result.error);\n      } else if (result.user) {\n        router.push('/dashboard');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleGoogleSignIn = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signInWithGoogle();\n      if (result.error) {\n        setError(result.error);\n      } else if (result.user) {\n        router.push('/dashboard');\n      }\n    } catch (err) {\n      setError('Google sign-in failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center px-4 relative\">\n      <div className=\"max-w-md w-full space-y-8 fade-in-up\">\n        <div className=\"text-center\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"w-20 h-20 rounded-full flex items-center justify-center relative overflow-hidden card glow\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20\"></div>\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-10 w-10 text-blue-400 relative z-10\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm3 1h6v6H6V5z\" clipRule=\"evenodd\" />\n                <path d=\"M6 5H5v1h1V5z\" />\n                <path d=\"M5 8h1v1H5V8z\" />\n                <path d=\"M8 5h1v1H8V5z\" />\n                <path d=\"M8 8h1v1H8V8z\" />\n                <path d=\"M11 5h1v1h-1V5z\" />\n                <path d=\"M11 8h1v1h-1V8z\" />\n                <path d=\"M5 11h1v1H5v-1z\" />\n                <path d=\"M8 11h1v1H8v-1z\" />\n                <path d=\"M11 11h1v1h-1v-1z\" />\n              </svg>\n            </div>\n          </div>\n          <h2 className=\"text-4xl font-bold gradient-text mb-2\">\n            {isSignUp ? 'Create Account' : 'Admin Login'}\n          </h2>\n          <p className=\"text-lg text-gray-300 font-light\">\n            {isSignUp\n              ? 'Create your account to access the Minecraft Server Admin Panel'\n              : 'Sign in to access the Minecraft Server Admin Panel'\n            }\n          </p>\n        </div>\n\n        <div className=\"card p-8 fade-in-up delay-200\">\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            {error && (\n              <div className=\"bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg backdrop-blur-sm\">\n                <div className=\"flex items-center\">\n                  <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {error}\n                </div>\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-semibold text-gray-200 mb-3\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                {...register('email', {\n                  required: 'Email is required',\n                  pattern: {\n                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                    message: 'Invalid email address'\n                  }\n                })}\n                className=\"input-field w-full\"\n                placeholder=\"Enter your email address\"\n              />\n              {errors.email && (\n                <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {errors.email.message}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-semibold text-gray-200 mb-3\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                {...register('password', {\n                  required: 'Password is required',\n                  minLength: {\n                    value: 6,\n                    message: 'Password must be at least 6 characters'\n                  }\n                })}\n                className=\"input-field w-full\"\n                placeholder=\"Enter your password\"\n              />\n              {errors.password && (\n                <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  {errors.password.message}\n                </p>\n              )}\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"btn-primary w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  {isSignUp ? 'Creating Account...' : 'Signing in...'}\n                </div>\n              ) : (\n                <span className=\"flex items-center justify-center\">\n                  <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n                  </svg>\n                  {isSignUp ? 'Create Account' : 'Sign In'}\n                </span>\n              )}\n            </button>\n\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-600\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-gray-900 text-gray-400\">Or continue with</span>\n              </div>\n            </div>\n\n            <button\n              type=\"button\"\n              onClick={handleGoogleSignIn}\n              disabled={isLoading}\n              className=\"w-full flex justify-center items-center px-4 py-3 border border-gray-600 rounded-lg shadow-sm bg-gray-800 text-sm font-medium text-gray-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n              </svg>\n              Sign in with Google\n            </button>\n          </form>\n\n          <div className=\"mt-8 text-center\">\n            <p className=\"text-sm text-gray-400\">\n              {isSignUp ? \"Already have an account?\" : \"Don't have an account?\"}\n              <button\n                type=\"button\"\n                onClick={() => setIsSignUp(!isSignUp)}\n                className=\"ml-2 text-blue-400 hover:text-blue-300 font-medium transition-colors duration-200\"\n              >\n                {isSignUp ? \"Sign in\" : \"Sign up\"}\n              </button>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEhE,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,WACX,MAAM,OAAO,KAAK,KAAK,EAAE,KAAK,QAAQ,IACtC,MAAM,OAAO,KAAK,KAAK,EAAE,KAAK,QAAQ;YAE1C,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK;YACvB,OAAO,IAAI,OAAO,IAAI,EAAE;gBACtB,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK;YACvB,OAAO,IAAI,OAAO,IAAI,EAAE;gBACtB,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAwC,SAAQ;wCAAY,MAAK;;0DACjH,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAiF,UAAS;;;;;;0DACrH,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACX,WAAW,mBAAmB;;;;;;sCAEjC,8OAAC;4BAAE,WAAU;sCACV,WACG,mEACA;;;;;;;;;;;;8BAKR,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;gCAC/C,uBACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAe,SAAQ;0DACxD,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoH,UAAS;;;;;;;;;;;4CAEzJ;;;;;;;;;;;;8CAKP,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAiD;;;;;;sDAGlF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,SAAS;gDACpB,UAAU;gDACV,SAAS;oDACP,OAAO;oDACP,SAAS;gDACX;4CACF,EAAE;4CACF,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAe,SAAQ;8DACxD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAoH,UAAS;;;;;;;;;;;gDAEzJ,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;8CAK3B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiD;;;;;;sDAGrF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,YAAY;gDACvB,UAAU;gDACV,WAAW;oDACT,OAAO;oDACP,SAAS;gDACX;4CACF,EAAE;4CACF,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAe,SAAQ;8DACxD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAoH,UAAS;;;;;;;;;;;gDAEzJ,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;8CAK9B,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAA6C,OAAM;gDAA6B,MAAK;gDAAO,SAAQ;;kEACjH,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAEpD,WAAW,wBAAwB;;;;;;6DAGtC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE,WAAW,mBAAmB;;;;;;;;;;;;8CAKrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;;;;;;8CAIrD,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAe,SAAQ;;8DACpC,8OAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,8OAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,8OAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;8DACvB,8OAAC;oDAAK,MAAK;oDAAU,GAAE;;;;;;;;;;;;wCACnB;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCACV,WAAW,6BAA6B;kDACzC,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,YAAY,CAAC;wCAC5B,WAAU;kDAET,WAAW,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC", "debugId": null}}]}