# Docker Helper Script for Minecraft Server Admin Panel
# PowerShell script to manage Docker Compose operations

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "logs", "build", "dev", "prod", "health", "clean")]
    [string]$Action,
    
    [string]$Service = "",
    [switch]$Follow
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Check-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." $Blue
    
    # Check if Docker is installed and running
    try {
        $dockerVersion = docker --version
        Write-ColorOutput "✅ Docker found: $dockerVersion" $Green
    } catch {
        Write-ColorOutput "❌ Docker is not installed or not running" $Red
        exit 1
    }
    
    # Check if Docker Compose is available
    try {
        $composeVersion = docker-compose --version
        Write-ColorOutput "✅ Docker Compose found: $composeVersion" $Green
    } catch {
        Write-ColorOutput "❌ Docker Compose is not available" $Red
        exit 1
    }
    
    # Check if .env file exists
    if (Test-Path ".env") {
        Write-ColorOutput "✅ Environment file found" $Green
    } else {
        Write-ColorOutput "⚠️  .env file not found. Creating from template..." $Yellow
        if (Test-Path ".env.docker") {
            Copy-Item ".env.docker" ".env"
            Write-ColorOutput "📝 Please edit .env with your Firebase configuration" $Yellow
        } else {
            Write-ColorOutput "❌ .env.docker template not found" $Red
            exit 1
        }
    }
}

function Start-Services {
    param([string]$Mode = "prod")
    
    Write-ColorOutput "🚀 Starting services in $Mode mode..." $Blue
    
    if ($Mode -eq "dev") {
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    } else {
        docker-compose up -d
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Services started successfully!" $Green
        Write-ColorOutput "🌐 Frontend: http://localhost:3001" $Blue
        Write-ColorOutput "🔧 Backend API: http://localhost:5000/api" $Blue
        Write-ColorOutput "❤️  Health Check: http://localhost:5000/api/health" $Blue
    } else {
        Write-ColorOutput "❌ Failed to start services" $Red
    }
}

function Stop-Services {
    Write-ColorOutput "🛑 Stopping services..." $Blue
    docker-compose down
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Services stopped successfully!" $Green
    } else {
        Write-ColorOutput "❌ Failed to stop services" $Red
    }
}

function Restart-Services {
    Write-ColorOutput "🔄 Restarting services..." $Blue
    docker-compose restart $Service
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Services restarted successfully!" $Green
    } else {
        Write-ColorOutput "❌ Failed to restart services" $Red
    }
}

function Show-Logs {
    Write-ColorOutput "📋 Showing logs for $Service..." $Blue
    
    if ($Follow) {
        docker-compose logs -f $Service
    } else {
        docker-compose logs $Service
    }
}

function Build-Services {
    Write-ColorOutput "🔨 Building services..." $Blue
    
    if ($Service) {
        docker-compose build --no-cache $Service
    } else {
        docker-compose build --no-cache
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Build completed successfully!" $Green
    } else {
        Write-ColorOutput "❌ Build failed" $Red
    }
}

function Check-Health {
    Write-ColorOutput "🏥 Checking service health..." $Blue
    
    # Check backend health
    try {
        $backendHealth = Invoke-RestMethod -Uri "http://localhost:5000/api/health" -Method Get
        Write-ColorOutput "✅ Backend: $($backendHealth.status)" $Green
        Write-ColorOutput "   Firebase: $($backendHealth.firebase_initialized)" $Blue
    } catch {
        Write-ColorOutput "❌ Backend: Not responding" $Red
    }
    
    # Check frontend (simple connection test)
    try {
        $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3001" -Method Head -TimeoutSec 5
        if ($frontendResponse.StatusCode -eq 200) {
            Write-ColorOutput "✅ Frontend: Responding" $Green
        }
    } catch {
        Write-ColorOutput "❌ Frontend: Not responding" $Red
    }
}

function Clean-Docker {
    Write-ColorOutput "🧹 Cleaning Docker resources..." $Blue
    
    # Stop and remove containers
    docker-compose down --remove-orphans
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful with this)
    $response = Read-Host "Remove unused volumes? This will delete data not in named volumes (y/N)"
    if ($response -eq "y" -or $response -eq "Y") {
        docker volume prune -f
    }
    
    Write-ColorOutput "✅ Cleanup completed!" $Green
}

# Main script execution
Write-ColorOutput "🐳 Minecraft Server Admin Panel - Docker Helper" $Blue
Write-ColorOutput "================================================" $Blue

Check-Prerequisites

switch ($Action) {
    "start" { Start-Services -Mode "prod" }
    "dev" { Start-Services -Mode "dev" }
    "prod" { Start-Services -Mode "prod" }
    "stop" { Stop-Services }
    "restart" { Restart-Services }
    "logs" { Show-Logs }
    "build" { Build-Services }
    "health" { Check-Health }
    "clean" { Clean-Docker }
    default { 
        Write-ColorOutput "❌ Unknown action: $Action" $Red
        Write-ColorOutput "Available actions: start, stop, restart, logs, build, dev, prod, health, clean" $Yellow
    }
}

Write-ColorOutput "================================================" $Blue
Write-ColorOutput "✨ Operation completed!" $Blue
