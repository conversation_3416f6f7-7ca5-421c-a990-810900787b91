'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../contexts/AuthContext';

export default function HomePage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (user) {
        router.push('/dashboard');
      } else {
        router.push('/login');
      }
    }
  }, [user, isLoading, router]);

  // Show loading state while checking authentication
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center fade-in-up">
        <div className="relative mb-6">
          <div className="animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto"></div>
          <div className="absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto"></div>
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">Loading</h3>
        <p className="text-gray-300 font-light">Redirecting...</p>
      </div>
    </div>
  );
}
