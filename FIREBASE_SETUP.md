# Firebase Authentication-Only Setup Guide

This guide will walk you through setting up Firebase Authentication for the Minecraft Server Admin Panel. This is a minimal, Auth-only configuration that excludes all other Firebase services (Storage, Analytics, Firestore, etc.) for optimal security and performance.

## Prerequisites

- Google account
- Node.js and npm installed
- Python 3.7+ installed
- Access to Google Firebase Console

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name (e.g., "minecraft-admin-panel")
4. **IMPORTANT**: Choose "Don't enable Google Analytics" for this Auth-only setup
5. Click "Create project"

> **Note**: We're deliberately excluding Google Analytics to maintain a minimal, Auth-only configuration.

## Step 2: Enable Authentication

1. In your Firebase project, click "Authentication" in the left sidebar
2. Click "Get started"
3. Go to the "Sign-in method" tab
4. Enable the following providers:
   - **Email/Password**: Click on it and toggle "Enable"
   - **Google**: Click on it, toggle "Enable", and set your project's public-facing name

## Step 3: Get Firebase Configuration

### Frontend Configuration

1. In Firebase Console, click the gear icon (Project settings)
2. Scroll down to "Your apps" section
3. Click "Add app" and select the web icon (`</>`)
4. Register your app with a nickname (e.g., "Admin Panel Web")
5. Copy the Firebase configuration object

### Backend Configuration (Service Account)

1. In Firebase Console, go to Project Settings → Service accounts
2. Click "Generate new private key"
3. Download the JSON file (keep it secure!)

## Step 4: Configure Environment Variables

### Frontend (.env.local)

Create a `.env.local` file in the project root:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000/api

# Firebase Configuration (Frontend) - Auth-only setup
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id_here
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id_here
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id_here
# Note: STORAGE_BUCKET and MEASUREMENT_ID removed for Auth-only setup
```

### Backend (backend/.env)

Create a `backend/.env` file:

```env
# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=1
FLASK_RELOADER_TYPE=stat
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# Firebase Admin Configuration
FIREBASE_PROJECT_ID=your_project_id_here
FIREBASE_PRIVATE_KEY_ID=your_private_key_id_here
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project_id.iam.gserviceaccount.com
FIREBASE_CLIENT_ID=your_client_id_here
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your_project_id.iam.gserviceaccount.com
```

## Step 5: Extract Service Account Information

From the downloaded service account JSON file, extract these values:

- `project_id` → `FIREBASE_PROJECT_ID`
- `private_key_id` → `FIREBASE_PRIVATE_KEY_ID`
- `private_key` → `FIREBASE_PRIVATE_KEY` (keep the quotes and newlines)
- `client_email` → `FIREBASE_CLIENT_EMAIL`
- `client_id` → `FIREBASE_CLIENT_ID`
- `auth_uri` → `FIREBASE_AUTH_URI`
- `token_uri` → `FIREBASE_TOKEN_URI`
- `auth_provider_x509_cert_url` → `FIREBASE_AUTH_PROVIDER_X509_CERT_URL`
- `client_x509_cert_url` → `FIREBASE_CLIENT_X509_CERT_URL`

## Step 6: Install Dependencies

### Frontend
```bash
npm install firebase
```

### Backend
```bash
cd backend
pip install firebase-admin pyjwt cryptography
```

## Step 7: Test the Setup

### Start the Backend
```bash
cd backend
python app.py
```

### Start the Frontend
```bash
npm run dev
```

### Test Authentication
1. Go to http://localhost:3000/login
2. Try creating an account with email/password
3. Try signing in with Google
4. Verify you can access the dashboard

## Step 8: Configure Firebase Authentication Security

In Firebase Console → Authentication → Settings:

1. **Authorized domains**: Add your production domain
2. **User actions**: Configure email verification if needed
3. **Password policy**: Set minimum requirements

### Enterprise-Grade Security Features Included

This Auth-only setup maintains all enterprise-grade security features:

- ✅ **Google Sign-In**: OAuth 2.0 with Google's security infrastructure
- ✅ **Email/Password Authentication**: Secure password hashing and validation
- ✅ **Token Verification**: JWT tokens with proper verification in backend
- ✅ **Rate Limiting**: Built-in Firebase Auth rate limiting
- ✅ **CSRF Protection**: Implemented through proper token handling
- ✅ **Session Management**: Secure session handling with token refresh

## Firebase Services Removed

This Auth-only configuration deliberately excludes the following Firebase services for optimal security and performance:

### Removed Services:
- ❌ **Cloud Firestore**: Database service (not needed for this application)
- ❌ **Realtime Database**: Real-time database service (not needed)
- ❌ **Cloud Storage**: File storage service (not needed)
- ❌ **Google Analytics**: Analytics and tracking (removed for privacy)
- ❌ **Cloud Functions**: Serverless functions (not needed)
- ❌ **Cloud Messaging**: Push notifications (not needed)
- ❌ **Remote Config**: Dynamic configuration (not needed)
- ❌ **Performance Monitoring**: Performance tracking (not needed)
- ❌ **App Check**: App attestation (not needed for this setup)

### Benefits of Auth-Only Setup:
1. **Reduced Attack Surface**: Fewer services mean fewer potential security vulnerabilities
2. **Better Performance**: Smaller bundle size and faster initialization
3. **Simplified Configuration**: Easier to manage and maintain
4. **Cost Optimization**: No charges for unused services
5. **Privacy Compliance**: No analytics or tracking data collection
6. **Focused Security**: All security efforts concentrated on authentication

### What Remains Fully Functional:
- Complete user authentication system
- Google OAuth integration
- Email/password authentication
- Token-based session management
- Backend token verification
- All enterprise-grade security features

## Troubleshooting

### Common Issues

1. **"Firebase not initialized"**
   - Check that all environment variables are set correctly
   - Verify the service account JSON values are properly formatted

2. **CORS errors**
   - Ensure your domain is added to Firebase authorized domains
   - Check that CORS is properly configured in the Flask backend

3. **Token verification fails**
   - Verify the service account has the correct permissions
   - Check that the private key is properly formatted with newlines

4. **Google Sign-in not working**
   - Ensure Google provider is enabled in Firebase Console
   - Check that the OAuth consent screen is configured

### Debug Mode

To enable debug logging, add to your backend `.env`:
```env
FLASK_LOG_LEVEL=DEBUG
```

## Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use different Firebase projects** for development and production
3. **Regularly rotate service account keys**
4. **Enable email verification** for production
5. **Set up proper Firebase Security Rules**
6. **Use HTTPS** in production
7. **Implement rate limiting** for authentication endpoints

## Production Deployment

For production deployment:

1. Create a separate Firebase project for production
2. Update environment variables with production values
3. Enable email verification
4. Configure proper CORS origins
5. Set up monitoring and logging
6. Implement backup strategies for user data

## Support

If you encounter issues:

1. Check the browser console for frontend errors
2. Check the Flask server logs for backend errors
3. Verify all environment variables are set correctly
4. Ensure Firebase project configuration matches your setup
