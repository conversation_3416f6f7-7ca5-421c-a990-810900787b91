'use client';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export interface ServerProperties {
  [key: string]: string;
}

export interface PlayerInfo {
  online: number;
  max: number;
  list: string[];
}

export interface Server {
  id: string;
  name: string;
  port: number;
  version: string;
  memory: string;
  status: string;
  backup?: boolean;
  container_id?: string;
  properties?: ServerProperties;
  players?: PlayerInfo;
}

export interface ServerFormData {
  name: string;
  port: number;
  version: string;
  memory: string;
}

export async function getServers(): Promise<Server[]> {
  try {
    console.log('Fetching servers from:', API_URL);
    const response = await fetch(`${API_URL}/servers`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch servers: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching servers:', error);
    throw error;
  }
}

export async function createServer(data: ServerFormData): Promise<Server> {
  try {
    const response = await fetch(`${API_URL}/servers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to create server: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating server:', error);
    throw error;
  }
}

export async function startServer(serverName: string): Promise<{ status: string; message: string }> {
  try {
    const response = await fetch(`${API_URL}/servers/${serverName}/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to start server: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error starting server ${serverName}:`, error);
    throw error;
  }
}

export async function stopServer(serverName: string): Promise<{ status: string; message: string }> {
  try {
    const response = await fetch(`${API_URL}/servers/${serverName}/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to stop server: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error stopping server ${serverName}:`, error);
    throw error;
  }
}

export async function deleteServer(serverName: string): Promise<{ message: string }> {
  try {
    const response = await fetch(`${API_URL}/servers/${serverName}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to delete server: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error deleting server ${serverName}:`, error);
    throw error;
  }
}

export async function toggleBackup(serverName: string): Promise<{ message: string }> {
  try {
    const response = await fetch(`${API_URL}/servers/${serverName}/backup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to toggle backup: ${response.status} ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Error toggling backup for server ${serverName}:`, error);
    throw error;
  }
}

export async function downloadBackup(serverName: string): Promise<void> {
  try {
    // Create a direct link to the backup endpoint
    const backupUrl = `${API_URL}/servers/${serverName}/backup`;

    // Open the URL in a new tab/window to trigger the download
    window.open(backupUrl, '_blank');
  } catch (error) {
    console.error(`Error downloading backup for server ${serverName}:`, error);
    throw error;
  }
}

export async function getServerDetails(serverId: string): Promise<Server> {
  try {
    const response = await fetch(`${API_URL}/servers/${serverId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to fetch server details: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching server details for ${serverId}:`, error);
    throw error;
  }
}

export async function getServerLogs(serverId: string): Promise<{ logs: string }> {
  try {
    const response = await fetch(`${API_URL}/servers/${serverId}/logs`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to fetch server logs: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching logs for server ${serverId}:`, error);
    throw error;
  }
}

export async function sendServerCommand(serverId: string, command: string): Promise<{ response: string }> {
  try {
    const response = await fetch(`${API_URL}/servers/${serverId}/command`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({ command }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to send command: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error sending command to server ${serverId}:`, error);
    throw error;
  }
}
