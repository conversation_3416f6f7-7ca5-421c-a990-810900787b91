'use client';

import Link from 'next/link';
import { useAuth } from '../contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function AdminHeader() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  return (
    <header className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90 backdrop-blur-sm"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>

      <div className="relative z-10 container mx-auto flex justify-between items-center p-6">
        <div className="flex items-center fade-in-up">
          <div className="w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-br from-blue-400/20 to-purple-400/20 backdrop-blur-sm border border-white/10">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-blue-300" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm3 1h6v6H6V5z" clipRule="evenodd" />
              <path d="M6 5H5v1h1V5z" />
              <path d="M5 8h1v1H5V8z" />
              <path d="M8 5h1v1H8V5z" />
              <path d="M8 8h1v1H8V8z" />
              <path d="M11 5h1v1h-1V5z" />
              <path d="M11 8h1v1h-1V8z" />
              <path d="M5 11h1v1H5v-1z" />
              <path d="M8 11h1v1H8v-1z" />
              <path d="M11 11h1v1h-1v-1z" />
            </svg>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">Minecraft Server Admin Panel</h1>
            <p className="text-blue-200 text-sm font-medium">Welcome, <span className="gradient-text-secondary">{user?.email || user?.displayName || 'User'}</span></p>
          </div>
        </div>

        <nav className="flex items-center space-x-4 fade-in-up delay-200">
          <Link
            href="/dashboard"
            className="nav-link text-white/90 hover:text-white font-medium px-4 py-2 rounded-xl hover:bg-white/10 transition-all duration-300"
          >
            <svg className="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
            Dashboard
          </Link>
          <button
            onClick={handleLogout}
            className="btn-secondary px-6 py-2 font-semibold inline-flex items-center hover:scale-105 transition-all duration-300"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
            Logout
          </button>
        </nav>
      </div>
    </header>
  );
}
