{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/lib/firebase.ts"], "sourcesContent": ["// Firebase configuration and initialization\nimport { initializeApp, getApps } from 'firebase/app';\nimport {\n  getAuth,\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  signInWithPopup,\n  GoogleAuthProvider,\n  signOut,\n  onAuthStateChanged,\n  User\n} from 'firebase/auth';\n\n// Firebase configuration - Auth-only setup\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  // Note: storageBucket and measurementId removed for Auth-only setup\n  // messagingSenderId and appId are required for proper Firebase initialization\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n};\n\n// Initialize Firebase\nconst app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\nconst auth = getAuth(app);\n\n// Configure Google Auth Provider\nconst googleProvider = new GoogleAuthProvider();\ngoogleProvider.setCustomParameters({\n  prompt: 'select_account'\n});\n\n// Auth functions\nexport const signInWithEmail = async (email: string, password: string) => {\n  try {\n    const result = await signInWithEmailAndPassword(auth, email, password);\n    return { user: result.user, error: null };\n  } catch (error: any) {\n    return { user: null, error: error.message };\n  }\n};\n\nexport const signUpWithEmail = async (email: string, password: string) => {\n  try {\n    const result = await createUserWithEmailAndPassword(auth, email, password);\n    return { user: result.user, error: null };\n  } catch (error: any) {\n    return { user: null, error: error.message };\n  }\n};\n\nexport const signInWithGoogle = async () => {\n  try {\n    const result = await signInWithPopup(auth, googleProvider);\n    return { user: result.user, error: null };\n  } catch (error: any) {\n    return { user: null, error: error.message };\n  }\n};\n\nexport const logOut = async () => {\n  try {\n    await signOut(auth);\n    return { error: null };\n  } catch (error: any) {\n    return { error: error.message };\n  }\n};\n\nexport const getCurrentUser = (): Promise<User | null> => {\n  return new Promise((resolve) => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      unsubscribe();\n      resolve(user);\n    });\n  });\n};\n\nexport const getIdToken = async (forceRefresh = false): Promise<string | null> => {\n  try {\n    const user = auth.currentUser;\n    if (user) {\n      return await user.getIdToken(forceRefresh);\n    }\n    return null;\n  } catch (error) {\n    console.error('Error getting ID token:', error);\n    return null;\n  }\n};\n\n// Auth state listener\nexport const onAuthStateChange = (callback: (user: User | null) => void) => {\n  return onAuthStateChanged(auth, callback);\n};\n\n// Export auth instance\nexport { auth };\nexport default app;\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;;;;;;;;;AAelC;AAdV;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAWA,2CAA2C;AAC3C,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,oEAAoE;IACpE,8EAA8E;IAC9E,iBAAiB;IACjB,KAAK;AACP;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,KAAK,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,uLAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;AACjF,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AAErB,iCAAiC;AACjC,MAAM,iBAAiB,IAAI,wNAAA,CAAA,qBAAkB;AAC7C,eAAe,mBAAmB,CAAC;IACjC,QAAQ;AACV;AAGO,MAAM,kBAAkB,OAAO,OAAe;IACnD,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,MAAM,OAAO;QAC7D,OAAO;YAAE,MAAM,OAAO,IAAI;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAEO,MAAM,kBAAkB,OAAO,OAAe;IACnD,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,MAAM,OAAO;QACjE,OAAO;YAAE,MAAM,OAAO,IAAI;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAEO,MAAM,mBAAmB;IAC9B,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,MAAM;QAC3C,OAAO;YAAE,MAAM,OAAO,IAAI;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAEO,MAAM,SAAS;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;QACd,OAAO;YAAE,OAAO;QAAK;IACvB,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,OAAO,MAAM,OAAO;QAAC;IAChC;AACF;AAEO,MAAM,iBAAiB;IAC5B,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,CAAC;YAC5C;YACA,QAAQ;QACV;IACF;AACF;AAEO,MAAM,aAAa,OAAO,eAAe,KAAK;IACnD,IAAI;QACF,MAAM,OAAO,KAAK,WAAW;QAC7B,IAAI,MAAM;YACR,OAAO,MAAM,KAAK,UAAU,CAAC;QAC/B;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;AAClC;;uCAIe", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { User } from 'firebase/auth';\nimport {\n  onAuthStateChange,\n  logOut,\n  getIdToken,\n  signInWithEmail,\n  signInWithGoogle,\n  signUpWithEmail\n} from '../lib/firebase';\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  signIn: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;\n  signUp: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;\n  signInWithGoogle: () => Promise<{ user: User | null; error: string | null }>;\n  logout: () => Promise<{ error: string | null }>;\n  getToken: (forceRefresh?: boolean) => Promise<string | null>;\n  refreshToken: () => Promise<string | null>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Listen for authentication state changes\n    const unsubscribe = onAuthStateChange((user) => {\n      setUser(user);\n      setIsLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    setIsLoading(true);\n    try {\n      const result = await signInWithEmail(email, password);\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signUp = async (email: string, password: string) => {\n    setIsLoading(true);\n    try {\n      const result = await signUpWithEmail(email, password);\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signInWithGoogleProvider = async () => {\n    setIsLoading(true);\n    try {\n      const result = await signInWithGoogle();\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      const result = await logOut();\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getToken = async (forceRefresh = false) => {\n    return await getIdToken(forceRefresh);\n  };\n\n  const refreshToken = async () => {\n    return await getIdToken(true);\n  };\n\n  const value = {\n    user,\n    isLoading,\n    signIn,\n    signUp,\n    signInWithGoogle: signInWithGoogleProvider,\n    logout,\n    getToken,\n    refreshToken\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAwBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,0CAA0C;YAC1C,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;sDAAE,CAAC;oBACrC,QAAQ;oBACR,aAAa;gBACf;;YAEA;0CAAO,IAAM;;QACf;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YAC5C,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YAC5C,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,2BAA2B;QAC/B,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD;YACpC,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,SAAM,AAAD;YAC1B,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO,eAAe,KAAK;QAC1C,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;IAC1B;IAEA,MAAM,eAAe;QACnB,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;IAC1B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA,kBAAkB;QAClB;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IA9Ea;KAAA", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/web-panel-blocksconnect/src/components/ParticleBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface Particle {\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  size: number;\n  opacity: number;\n}\n\nexport default function ParticleBackground() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particlesRef = useRef<Particle[]>([]);\n  const animationRef = useRef<number>();\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    const createParticles = () => {\n      const particles: Particle[] = [];\n      const particleCount = Math.min(50, Math.floor((canvas.width * canvas.height) / 15000));\n\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          vx: (Math.random() - 0.5) * 0.5,\n          vy: (Math.random() - 0.5) * 0.5,\n          size: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2,\n        });\n      }\n\n      particlesRef.current = particles;\n    };\n\n    const drawParticle = (particle: Particle) => {\n      ctx.save();\n      ctx.globalAlpha = particle.opacity;\n      \n      // Create gradient for particle\n      const gradient = ctx.createRadialGradient(\n        particle.x, particle.y, 0,\n        particle.x, particle.y, particle.size * 2\n      );\n      gradient.addColorStop(0, '#3b82f6');\n      gradient.addColorStop(0.5, '#8b5cf6');\n      gradient.addColorStop(1, 'transparent');\n      \n      ctx.fillStyle = gradient;\n      ctx.beginPath();\n      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n      ctx.fill();\n      \n      ctx.restore();\n    };\n\n    const drawConnections = () => {\n      const particles = particlesRef.current;\n      const maxDistance = 120;\n\n      for (let i = 0; i < particles.length; i++) {\n        for (let j = i + 1; j < particles.length; j++) {\n          const dx = particles[i].x - particles[j].x;\n          const dy = particles[i].y - particles[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < maxDistance) {\n            const opacity = (1 - distance / maxDistance) * 0.2;\n            \n            ctx.save();\n            ctx.globalAlpha = opacity;\n            ctx.strokeStyle = '#3b82f6';\n            ctx.lineWidth = 0.5;\n            ctx.beginPath();\n            ctx.moveTo(particles[i].x, particles[i].y);\n            ctx.lineTo(particles[j].x, particles[j].y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        }\n      }\n    };\n\n    const updateParticles = () => {\n      const particles = particlesRef.current;\n\n      particles.forEach(particle => {\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        // Bounce off edges\n        if (particle.x < 0 || particle.x > canvas.width) {\n          particle.vx *= -1;\n        }\n        if (particle.y < 0 || particle.y > canvas.height) {\n          particle.vy *= -1;\n        }\n\n        // Keep particles within bounds\n        particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n        particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n\n        // Subtle opacity animation\n        particle.opacity += (Math.random() - 0.5) * 0.01;\n        particle.opacity = Math.max(0.1, Math.min(0.7, particle.opacity));\n      });\n    };\n\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      updateParticles();\n      drawConnections();\n      \n      particlesRef.current.forEach(drawParticle);\n      \n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    // Initialize\n    resizeCanvas();\n    createParticles();\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      resizeCanvas();\n      createParticles();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"particle-background\"\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: -1,\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAae,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,MAAM;6DAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA,MAAM;gEAAkB;oBACtB,MAAM,YAAwB,EAAE;oBAChC,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,MAAM,GAAI;oBAE/E,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,UAAU,IAAI,CAAC;4BACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;4BAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;4BAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;4BAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;wBACjC;oBACF;oBAEA,aAAa,OAAO,GAAG;gBACzB;;YAEA,MAAM;6DAAe,CAAC;oBACpB,IAAI,IAAI;oBACR,IAAI,WAAW,GAAG,SAAS,OAAO;oBAElC,+BAA+B;oBAC/B,MAAM,WAAW,IAAI,oBAAoB,CACvC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,GACxB,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,GAAG;oBAE1C,SAAS,YAAY,CAAC,GAAG;oBACzB,SAAS,YAAY,CAAC,KAAK;oBAC3B,SAAS,YAAY,CAAC,GAAG;oBAEzB,IAAI,SAAS,GAAG;oBAChB,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;oBAC5D,IAAI,IAAI;oBAER,IAAI,OAAO;gBACb;;YAEA,MAAM;gEAAkB;oBACtB,MAAM,YAAY,aAAa,OAAO;oBACtC,MAAM,cAAc;oBAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;wBACzC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;4BAC7C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;4BAC1C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;4BAC1C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,aAAa;gCAC1B,MAAM,UAAU,CAAC,IAAI,WAAW,WAAW,IAAI;gCAE/C,IAAI,IAAI;gCACR,IAAI,WAAW,GAAG;gCAClB,IAAI,WAAW,GAAG;gCAClB,IAAI,SAAS,GAAG;gCAChB,IAAI,SAAS;gCACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;gCACzC,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;gCACzC,IAAI,MAAM;gCACV,IAAI,OAAO;4BACb;wBACF;oBACF;gBACF;;YAEA,MAAM;gEAAkB;oBACtB,MAAM,YAAY,aAAa,OAAO;oBAEtC,UAAU,OAAO;wEAAC,CAAA;4BAChB,SAAS,CAAC,IAAI,SAAS,EAAE;4BACzB,SAAS,CAAC,IAAI,SAAS,EAAE;4BAEzB,mBAAmB;4BACnB,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE;gCAC/C,SAAS,EAAE,IAAI,CAAC;4BAClB;4BACA,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE;gCAChD,SAAS,EAAE,IAAI,CAAC;4BAClB;4BAEA,+BAA+B;4BAC/B,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC;4BAC1D,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,SAAS,CAAC;4BAE3D,2BAA2B;4BAC3B,SAAS,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC5C,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,SAAS,OAAO;wBACjE;;gBACF;;YAEA,MAAM;wDAAU;oBACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C;oBACA;oBAEA,aAAa,OAAO,CAAC,OAAO,CAAC;oBAE7B,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA,aAAa;YACb;YACA;YACA;YAEA,gBAAgB;YAChB,MAAM;6DAAe;oBACnB;oBACA;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;gDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;uCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,QAAQ,CAAC;QACX;;;;;;AAGN;GA3JwB;KAAA", "debugId": null}}]}