# Docker Setup Guide for Minecraft Server Admin Panel

This guide explains how to run the Minecraft Server Admin Panel using Docker Compose with the Firebase Auth-only configuration.

## Prerequisites

- Docker and Docker Compose installed
- Firebase project configured for Authentication only (see FIREBASE_SETUP.md)
- Your Firebase configuration values

## Quick Start

### 1. Configure Environment Variables

Copy the Docker environment template:
```bash
cp .env.docker .env
```

Edit `.env` and replace the placeholder values with your actual Firebase configuration:

```env
# Firebase Configuration (Frontend) - Auth-only setup
NEXT_PUBLIC_FIREBASE_API_KEY=your_actual_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_actual_project_id
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_actual_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_actual_app_id

# Firebase Admin Configuration (Backend)
FIREBASE_PROJECT_ID=your_actual_project_id
FIREBASE_PRIVATE_KEY_ID=your_actual_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_actual_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project_id.iam.gserviceaccount.com
FIREBASE_CLIENT_ID=your_actual_client_id
# ... other Firebase admin config
```

### 2. Run the Application

**Production Mode:**
```bash
docker-compose up -d
```

**Development Mode:**
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### 3. Access the Application

- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:5000/api
- **Health Check**: http://localhost:5000/api/health

## Architecture

### Services

1. **Frontend (Next.js)**
   - Port: 3001
   - Firebase Auth-only configuration
   - Production-optimized build with standalone output
   - Health checks enabled

2. **Backend (Flask)**
   - Port: 5000
   - Firebase Admin SDK for token verification
   - RESTful API for server management
   - Health checks enabled

### Networking

- Both services run on a custom bridge network: `minecraft-admin-network`
- Frontend communicates with backend via internal Docker networking
- External access via exposed ports

### Volumes

- `minecraft-servers`: Persistent storage for Minecraft server data
- `./logs`: Development logs (development mode only)

## Configuration Files

### Docker Compose Files

- `docker-compose.yml`: Main production configuration
- `docker-compose.dev.yml`: Development overrides
- `.env.docker`: Environment template
- `.env`: Your actual environment variables (not in git)

### Dockerfiles

- `Dockerfile.frontend`: Production Next.js build
- `Dockerfile.frontend.dev`: Development Next.js setup
- `backend/Dockerfile`: Production Flask setup

## Development vs Production

### Production Mode
```bash
docker-compose up -d
```
- Optimized builds
- No source code mounting
- Production environment variables
- Health checks enabled
- Automatic restarts

### Development Mode
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```
- Source code mounted for hot reloading
- Development environment variables
- Debug logging enabled
- Interactive terminals

## Firebase Auth-Only Features

This setup maintains all Firebase Authentication features while excluding other services:

✅ **Included:**
- Google Sign-In OAuth 2.0
- Email/Password authentication
- JWT token verification
- Rate limiting
- CSRF protection
- Session management

❌ **Excluded:**
- Cloud Firestore
- Cloud Storage
- Google Analytics
- Cloud Functions
- Cloud Messaging
- Remote Config

## Health Checks

Both services include health check endpoints:

- **Frontend**: `GET /api/health` (via Next.js API routes)
- **Backend**: `GET /api/health`

Health checks verify:
- Service responsiveness
- Firebase initialization status
- Basic functionality

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check if ports are in use
   netstat -an | findstr :3001
   netstat -an | findstr :5000
   ```

2. **Firebase configuration errors**
   ```bash
   # Check backend logs
   docker-compose logs backend
   
   # Check frontend logs
   docker-compose logs frontend
   ```

3. **Environment variables not loaded**
   ```bash
   # Verify .env file exists and has correct values
   cat .env
   
   # Restart services
   docker-compose down && docker-compose up -d
   ```

### Logs

View service logs:
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f frontend
docker-compose logs -f backend
```

### Rebuilding

Force rebuild after code changes:
```bash
# Rebuild all services
docker-compose build --no-cache

# Rebuild specific service
docker-compose build --no-cache frontend
```

## Security Considerations

1. **Environment Variables**: Never commit `.env` files with real credentials
2. **Network Security**: Services communicate via internal Docker network
3. **User Permissions**: Backend runs as non-root user
4. **Firebase Auth**: Only Authentication services enabled
5. **CORS**: Properly configured for frontend-backend communication

## Scaling

To scale services:
```bash
# Scale frontend to 2 instances
docker-compose up -d --scale frontend=2

# Scale backend to 3 instances
docker-compose up -d --scale backend=3
```

Note: You'll need a load balancer for multiple frontend instances.

## Backup and Persistence

- Minecraft server data persists in the `minecraft-servers` volume
- To backup: `docker run --rm -v minecraft-servers:/data -v $(pwd):/backup alpine tar czf /backup/servers-backup.tar.gz -C /data .`
- To restore: `docker run --rm -v minecraft-servers:/data -v $(pwd):/backup alpine tar xzf /backup/servers-backup.tar.gz -C /data`

## Support

For issues:
1. Check service logs: `docker-compose logs`
2. Verify environment configuration
3. Ensure Firebase project is properly configured
4. Check network connectivity between services
